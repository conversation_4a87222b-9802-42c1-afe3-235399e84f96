from agents import Agent, Runner, handoffs
from config import model

history_tutor_agent = Agent(
    name="History Agent",
    model=model,
    handoff_description="Specialist agent for historical questions",
    instructions="You provide assistance with historical queries. Explain important events and context clearly.",
)

math_tutor_agent = Agent(
    name="Math Tutor",
    model=model,
    handoff_description="Specialist agent for math questions",
    instructions="You provide help with math problems. Explain your reasoning at each step and include examples",
)

triage_agent = Agent(  # Triage agent means all in one
    name="Triage Agent",
    model=model,
    instructions="You determine which agent to use based on the user's homework question",
    handoffs=[math_tutor_agent, history_tutor_agent]
)


agent = Agent(
    name="Help<PERSON> Huzaifa's Agent",
    instructions="You are helpful assistant and and know everything in this world everything's. Explain your reasoning at each step and include examples",
)


async def main():
    prompt = input("\nEnter your prompt: ")
    result = await Runner.run(triage_agent, prompt)
    print(result.final_output)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
